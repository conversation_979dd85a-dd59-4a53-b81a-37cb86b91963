"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/logs/page",{

/***/ "(app-pages-browser)/./src/components/logs/LogDetailModal.tsx":
/*!************************************************!*\
  !*** ./src/components/logs/LogDetailModal.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LogDetailModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\nconst DetailItem = (param)=>{\n    let { label, value } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-2 sm:grid sm:grid-cols-3 sm:gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                className: \"text-sm font-medium text-gray-300\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                lineNumber: 36,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                className: \"mt-1 text-sm text-white sm:mt-0 sm:col-span-2 break-words\",\n                children: value !== null && value !== undefined && value !== '' ? value : 'N/A'\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined);\n};\n_c = DetailItem;\nconst PayloadDisplay = (param)=>{\n    let { title, data } = param;\n    let content;\n    if (data === null || data === undefined) {\n        content = 'N/A';\n    } else if (typeof data === 'string') {\n        content = data;\n    } else {\n        try {\n            content = JSON.stringify(data, null, 2);\n        } catch (e) {\n            content = 'Invalid JSON data';\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                className: \"text-sm font-medium text-gray-300 mb-1\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                className: \"mt-1 text-sm text-gray-300 bg-gray-800/50 p-3 rounded-lg border border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    className: \"whitespace-pre-wrap break-all\",\n                    children: content\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = PayloadDisplay;\nfunction LogDetailModal(param) {\n    let { log, onClose, apiConfigNameMap } = param;\n    if (!log) return null;\n    const getStatusDisplay = (statusCode)=>{\n        if (statusCode === null) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"px-2 py-0.5 rounded-full text-xs bg-gray-600 text-gray-100\",\n            children: \"N/A\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n            lineNumber: 68,\n            columnNumber: 37\n        }, this);\n        if (statusCode >= 200 && statusCode < 300) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"px-2 py-0.5 rounded-full text-xs bg-green-600 text-green-100\",\n            children: [\n                \"Success (\",\n                statusCode,\n                \")\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n            lineNumber: 69,\n            columnNumber: 55\n        }, this);\n        if (statusCode >= 400) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"px-2 py-0.5 rounded-full text-xs bg-red-600 text-red-100\",\n            children: [\n                \"Error (\",\n                statusCode,\n                \")\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n            lineNumber: 70,\n            columnNumber: 35\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"px-2 py-0.5 rounded-full text-xs bg-yellow-500 text-yellow-100\",\n            children: [\n                \"Other (\",\n                statusCode,\n                \")\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n            lineNumber: 71,\n            columnNumber: 12\n        }, this);\n    };\n    const apiModelName = log.custom_api_config_id ? apiConfigNameMap[log.custom_api_config_id] || 'Unknown Model' : 'N/A';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50 transition-opacity duration-300 ease-in-out\",\n        onClick: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-900/95 backdrop-blur-sm rounded-lg border border-gray-800/50 max-w-2xl w-full max-h-[90vh] flex flex-col\",\n            onClick: (e)=>e.stopPropagation(),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center p-6 border-b border-gray-800/50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-white\",\n                            children: [\n                                \"Log Details (ID: \",\n                                log.id.substring(0, 8),\n                                \"...)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-4 overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                            className: \"divide-y divide-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                    label: \"Timestamp\",\n                                    value: new Date(log.request_timestamp).toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                    label: \"API Model Used\",\n                                    value: apiModelName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                    label: \"Role Requested\",\n                                    value: log.role_requested\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                    label: \"Role Used\",\n                                    value: log.role_used\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                    label: \"Status\",\n                                    value: getStatusDisplay(log.status_code)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                    label: \"LLM Provider\",\n                                    value: log.llm_provider_name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                    label: \"LLM Model Name\",\n                                    value: (()=>{\n                                        var _log_role_used, _log_response_payload_summary;\n                                        // Check if this is an orchestration log\n                                        if (((_log_role_used = log.role_used) === null || _log_role_used === void 0 ? void 0 : _log_role_used.includes('RouKey_Multi Roles_')) && ((_log_response_payload_summary = log.response_payload_summary) === null || _log_response_payload_summary === void 0 ? void 0 : _log_response_payload_summary.models_used)) {\n                                            const models = log.response_payload_summary.models_used;\n                                            if (models.length <= 3) {\n                                                return models.join(', ');\n                                            } else {\n                                                return \"\".concat(models.slice(0, 3).join(', '), \"...\");\n                                            }\n                                        }\n                                        return log.llm_model_name;\n                                    })()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                    label: \"LLM Latency\",\n                                    value: log.llm_provider_latency_ms !== null ? \"\".concat(log.llm_provider_latency_ms, \" ms\") : 'N/A'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                    label: \"RoKey Latency\",\n                                    value: log.processing_duration_ms !== null ? \"\".concat(log.processing_duration_ms, \" ms\") : 'N/A'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                    label: \"Input Tokens\",\n                                    value: log.input_tokens !== null ? log.input_tokens : 'N/A'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                    label: \"Output Tokens\",\n                                    value: log.output_tokens !== null ? log.output_tokens : 'N/A'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                    label: \"Cost\",\n                                    value: log.cost !== null ? \"$\".concat(log.cost.toFixed(6)) : 'N/A'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                    label: \"Multimodal Request\",\n                                    value: log.is_multimodal ? 'Yes' : 'No'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                    label: \"IP Address\",\n                                    value: log.ip_address\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                log.user_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                    label: \"User ID\",\n                                    value: log.user_id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 29\n                                }, this),\n                                log.error_message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                            label: \"Error Message\",\n                                            value: log.error_message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                            label: \"Error Source\",\n                                            value: log.error_source\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                log.llm_provider_status_code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailItem, {\n                                    label: \"LLM Provider Status\",\n                                    value: log.llm_provider_status_code\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 46\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        log.request_payload_summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PayloadDisplay, {\n                            title: \"Request Payload Summary\",\n                            data: log.request_payload_summary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, this),\n                        log.response_payload_summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PayloadDisplay, {\n                            title: \"Response Payload Summary\",\n                            data: log.response_payload_summary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, this),\n                        log.error_details_zod && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PayloadDisplay, {\n                            title: \"Zod Validation Error Details\",\n                            data: log.error_details_zod\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-t border-gray-800/50 text-right\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 bg-gray-800/50 text-gray-300 border border-gray-700 rounded-lg hover:border-gray-600 transition-all duration-200\",\n                        children: \"Close\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\logs\\\\LogDetailModal.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_c2 = LogDetailModal;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"DetailItem\");\n$RefreshReg$(_c1, \"PayloadDisplay\");\n$RefreshReg$(_c2, \"LogDetailModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/logs/LogDetailModal.tsx\n"));

/***/ })

});