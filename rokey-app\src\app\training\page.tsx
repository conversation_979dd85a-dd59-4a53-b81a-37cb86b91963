'use client';

import { useState, useEffect } from 'react';
import { type CustomApiConfig } from '@/types/customApiConfigs';
import { type TrainingJob, type TrainingPrompt } from '@/types/training';
import DocumentUpload from '@/components/DocumentUpload';
import { TierGuard, TierBadge, LimitIndicator } from '@/components/TierEnforcement';
import { useSubscription } from '@/hooks/useSubscription';
import { useConfirmation } from '@/hooks/useConfirmation';
import ConfirmationModal from '@/components/ui/ConfirmationModal';


export default function TrainingPage() {
  // Subscription hook
  const { subscriptionStatus } = useSubscription();
  const confirmation = useConfirmation();

  // State management
  const [customConfigs, setCustomConfigs] = useState<CustomApiConfig[]>([]);
  const [selectedConfigId, setSelectedConfigId] = useState<string>('');
  const [trainingJobs, setTrainingJobs] = useState<TrainingJob[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [documentCount, setDocumentCount] = useState<number>(0);

  // Prompt engineering form state
  const [trainingPrompts, setTrainingPrompts] = useState<string>('');



  // Load existing training data for a configuration
  const loadExistingTrainingData = async (configId: string) => {
    if (!configId) return;

    try {
      // Load training jobs
      const jobsResponse = await fetch(`/api/training/jobs?custom_api_config_id=${configId}`);
      if (jobsResponse.ok) {
        const jobs = await jobsResponse.json();
        if (jobs.length > 0) {
          const latestJob = jobs[0];

          // Load training prompts
          if (latestJob.training_data?.raw_prompts) {
            setTrainingPrompts(latestJob.training_data.raw_prompts);
          }


        }
      }
    } catch (err: any) {
      console.warn('Failed to load existing training data:', err);
    }
  };

  // Fetch custom API configs on component mount
  useEffect(() => {
    const fetchConfigs = async () => {
      try {
        const response = await fetch('/api/custom-configs');
        if (!response.ok) {
          throw new Error('Failed to fetch configurations');
        }
        const data: CustomApiConfig[] = await response.json();
        setCustomConfigs(data);
        if (data.length > 0) {
          setSelectedConfigId(data[0].id);
          loadExistingTrainingData(data[0].id);
        }
      } catch (err: any) {
        setError(`Failed to load configurations: ${err.message}`);
      }
    };
    fetchConfigs();
  }, []);

  // Load training data when configuration changes
  useEffect(() => {
    if (selectedConfigId) {
      loadExistingTrainingData(selectedConfigId);
    }
  }, [selectedConfigId]);

  // Fetch document count for a configuration
  const fetchDocumentCount = async (configId: string, retryCount = 0) => {
    try {
      // Add cache-busting parameter to ensure fresh data
      const timestamp = Date.now();
      const response = await fetch(`/api/documents/list?configId=${configId}&_t=${timestamp}`, {
        cache: 'no-store' // Ensure we don't get cached responses
      });
      if (response.ok) {
        const data = await response.json();
        const newCount = data.documents?.length || 0;
        console.log(`[Training Page] Document count updated: ${newCount} for config: ${configId}`);
        setDocumentCount(newCount);
      } else {
        console.error('Error fetching document count:', response.status, response.statusText);
        // Retry once if the request failed
        if (retryCount < 1) {
          setTimeout(() => fetchDocumentCount(configId, retryCount + 1), 1000);
        }
      }
    } catch (err) {
      console.error('Error fetching document count:', err);
      // Retry once if there was an error
      if (retryCount < 1) {
        setTimeout(() => fetchDocumentCount(configId, retryCount + 1), 1000);
      }
    }
  };

  // Fetch document count when config changes
  useEffect(() => {
    if (selectedConfigId) {
      fetchDocumentCount(selectedConfigId);
    }
  }, [selectedConfigId]);

  // Process training prompts into structured format
  const processTrainingPrompts = (prompts: string) => {
    const processed = {
      system_instructions: '',
      examples: [] as Array<{input: string, output: string}>,
      behavior_guidelines: '',
      general_instructions: ''
    };

    const lines = prompts.split('\n').filter(line => line.trim());

    for (const line of lines) {
      const trimmedLine = line.trim();

      if (trimmedLine.startsWith('SYSTEM:')) {
        processed.system_instructions += trimmedLine.replace('SYSTEM:', '').trim() + '\n';
      } else if (trimmedLine.startsWith('BEHAVIOR:')) {
        processed.behavior_guidelines += trimmedLine.replace('BEHAVIOR:', '').trim() + '\n';
      } else if (trimmedLine.includes('→') || trimmedLine.includes('->')) {
        // Parse example: "User input → Expected response"
        const separator = trimmedLine.includes('→') ? '→' : '->';
        const parts = trimmedLine.split(separator);
        if (parts.length >= 2) {
          const input = parts[0].trim();
          const output = parts.slice(1).join(separator).trim();
          processed.examples.push({ input, output });
        }
      } else if (trimmedLine.length > 0) {
        // General instruction
        processed.general_instructions += trimmedLine + '\n';
      }
    }

    return processed;
  };

  // Handle training job creation or update
  const handleStartTraining = async () => {
    if (!selectedConfigId || !trainingPrompts.trim()) {
      setError('Please select an API configuration and provide training prompts.');
      return;
    }

    // Prevent multiple simultaneous training operations
    if (isLoading) {
      console.warn('[Training] Operation already in progress, ignoring duplicate request');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // Process training prompts
      const processedPrompts = processTrainingPrompts(trainingPrompts);

      // Generate a meaningful name based on content
      const configName = customConfigs.find(c => c.id === selectedConfigId)?.name || 'Unknown Config';

      // Prepare training job data
      const trainingJobData = {
        custom_api_config_id: selectedConfigId,
        name: `${configName} Training - ${new Date().toLocaleDateString()}`,
        description: `Training job for ${configName} with ${processedPrompts.examples.length} examples`,
        training_data: {
          processed_prompts: processedPrompts,
          raw_prompts: trainingPrompts.trim(),
          last_prompt_update: new Date().toISOString()
        },
        parameters: {
          training_type: 'prompt_engineering',
          created_via: 'training_page',
          version: '1.0'
        }
      };

      // Use UPSERT to handle both create and update scenarios
      const response = await fetch('/api/training/jobs/upsert', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(trainingJobData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[Training] Failed to upsert training job:', errorText);
        throw new Error(`Failed to save training job: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      const isUpdate = result.operation === 'updated';

      console.log(`[Training] Successfully ${isUpdate ? 'updated' : 'created'} training job:`, result.id);

      // Show success message based on operation type
      const operationText = isUpdate ? 'updated' : 'enhanced';
      const operationEmoji = isUpdate ? '🔄' : '🎉';

      const successMessage = `${operationEmoji} Prompt Engineering ${isUpdate ? 'updated' : 'completed'} successfully!\n\n` +
        `Your "${configName}" configuration has been ${operationText} with:\n` +
        `• ${processedPrompts.examples.length} training examples\n` +
        `• Custom system instructions and behavior guidelines\n` +
        `\n✨ All future chats using this configuration will automatically:\n` +
        `• Follow your training examples\n` +
        `• Apply your behavior guidelines\n` +
        `• Maintain consistent personality and responses\n\n` +
        `🚀 Try it now in the Playground to see your ${isUpdate ? 'updated' : 'enhanced'} model in action!\n\n` +
        `💡 Your training prompts remain here so you can modify them anytime.`;

      setSuccessMessage(successMessage);

    } catch (err: any) {
      console.error('Error in training operation:', err);
      setError(`Failed to create prompt engineering: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen w-full bg-[#040716] text-white overflow-x-hidden">
      {/* Header Section - Following analytics design */}
      <div className="border-b border-gray-800/50">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              <h1 className="text-2xl font-semibold text-white">Training</h1>
              <div className="flex items-center space-x-1">
                <button className="px-3 py-1 text-sm text-gray-400 hover:text-white transition-colors">
                  Prompt Engineering
                </button>
                <button className="px-3 py-1 text-sm bg-cyan-500 text-white rounded">
                  Knowledge Base
                </button>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {subscriptionStatus && (
                <TierBadge tier={subscriptionStatus.tier} size="lg" />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Welcome Section with Gradient */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400">
              AI Training & Enhancement
            </span>
          </h1>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto">
            Enhance your AI models with custom prompts and knowledge documents. Upload your proprietary content to create domain-specific AI assistants.
          </p>
        </div>
      </div>

      <div className="w-full px-4 sm:px-6 lg:px-8 pb-8">
        <div className="max-w-6xl mx-auto">

        {/* Error/Success Messages */}
        {error && (
          <div className="mb-6 bg-red-900/20 border border-red-500/30 rounded-xl p-4">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-red-200 text-sm font-medium">{error}</p>
            </div>
          </div>
        )}

        {successMessage && (
          <div className="mb-6 bg-green-900/20 border border-green-500/30 rounded-xl p-4">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <p className="text-green-200 text-sm font-medium">{successMessage}</p>
            </div>
          </div>
        )}

        {/* Document Upload Section */}
        <TierGuard
          feature="knowledge_base"
          customMessage="Knowledge base document upload is available starting with the Professional plan. Upload documents to enhance your AI with proprietary knowledge and create domain-specific assistants."
        >
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-8 mb-8 border border-gray-800/50">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-orange-500/20 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Knowledge Documents</h2>
                <p className="text-sm text-gray-400">Upload documents to enhance your AI with proprietary knowledge</p>
              </div>
            </div>

            {/* Document Upload Limits - Subtle inline indicator */}
            {subscriptionStatus && selectedConfigId && (
              <div className="mb-4 bg-gray-800/50 border border-gray-700/50 rounded-lg px-4 py-2">
                <LimitIndicator
                  current={documentCount}
                  limit={subscriptionStatus.tier === 'professional' ? 5 :
                         subscriptionStatus.tier === 'enterprise' ? 999999 : 0}
                  label="Knowledge Base Documents"
                  tier={subscriptionStatus.tier}
                  showUpgradeHint={true}
                />
              </div>
            )}

            <DocumentUpload
              configId={selectedConfigId}
              onDocumentUploaded={() => {
                console.log('Document uploaded successfully');
                // Add a small delay to ensure database consistency before refreshing count
                if (selectedConfigId) {
                  setTimeout(() => {
                    fetchDocumentCount(selectedConfigId);
                  }, 500); // 500ms delay
                }
              }}
              onDocumentDeleted={() => {
                console.log('Document deleted successfully');
                // Add a small delay to ensure database consistency before refreshing count
                if (selectedConfigId) {
                  setTimeout(() => {
                    fetchDocumentCount(selectedConfigId);
                  }, 500); // 500ms delay
                }
              }}
            />
          </div>
        </TierGuard>

        {/* Training Form */}
        <TierGuard
          feature="prompt_engineering"
          customMessage="Prompt engineering is available starting with the Starter plan. Create custom prompts to define AI behavior, provide examples, and enhance your model's responses."
        >
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-8 border border-gray-800/50">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Custom Prompts</h2>
                <p className="text-sm text-gray-400">Define behavior, examples, and instructions for your AI</p>
              </div>
            </div>

          <div className="space-y-8">
            {/* API Configuration Selection */}
            <div>
              <label htmlFor="configSelect" className="block text-sm font-medium text-gray-300 mb-2">
                Select API Configuration
              </label>
              <select
                id="configSelect"
                value={selectedConfigId}
                onChange={(e) => setSelectedConfigId(e.target.value)}
                className="w-full px-4 py-3 border border-gray-700 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 transition-colors bg-gray-800 text-white text-sm"
              >
                <option value="">Choose which model to train...</option>
                {customConfigs.map((config) => (
                  <option key={config.id} value={config.id}>
                    {config.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Training Prompts */}
            <div>
              <label htmlFor="trainingPrompts" className="block text-sm font-medium text-gray-300 mb-2">
                Custom Prompts & Instructions
              </label>
              <textarea
                id="trainingPrompts"
                value={trainingPrompts}
                onChange={(e) => setTrainingPrompts(e.target.value)}
                placeholder={`Enter your training prompts using these formats:

SYSTEM: You are a helpful customer service agent for our company
BEHAVIOR: Always be polite and offer solutions

User asks about returns → I'd be happy to help with your return! Let me check our policy for you.
Customer is frustrated → I understand your frustration. Let me see how I can resolve this for you.

General instructions can be written as regular text.`}
                rows={12}
                className="w-full px-4 py-3 border border-gray-700 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 transition-colors bg-gray-800 text-white text-sm resize-none font-mono placeholder-gray-500"
              />
              <div className="mt-3 bg-blue-900/20 border border-blue-500/30 rounded-lg p-3">
                <h4 className="text-sm font-medium text-blue-300 mb-2">Training Format Guide:</h4>
                <ul className="text-xs text-blue-200 space-y-1">
                  <li><strong>SYSTEM:</strong> Core instructions for the AI's role and personality</li>
                  <li><strong>BEHAVIOR:</strong> Guidelines for how the AI should behave</li>
                  <li><strong>Examples:</strong> Use "User input → Expected response" format</li>
                  <li><strong>General:</strong> Any other instructions written as normal text</li>
                </ul>
              </div>
            </div>



            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-6 border-t border-gray-700/50">
              <div className="flex space-x-3">
                <button
                  type="button"
                  className="btn-secondary"
                  onClick={() => {
                    confirmation.showConfirmation(
                      {
                        title: 'Clear Training Prompts',
                        message: 'Are you sure you want to clear all training prompts? This will remove all your custom instructions, examples, and behavior guidelines. This action cannot be undone.',
                        confirmText: 'Clear All',
                        cancelText: 'Cancel',
                        type: 'warning'
                      },
                      () => {
                        setTrainingPrompts('');
                      }
                    );
                  }}
                >
                  Clear Form
                </button>
              </div>

              <button
                type="button"
                onClick={handleStartTraining}
                disabled={!selectedConfigId || !trainingPrompts.trim() || isLoading}
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Processing Prompts...
                  </>
                ) : (
                  'Save Prompts'
                )}
              </button>
            </div>
          </div>
          </div>
        </TierGuard>

        {/* Confirmation Modal */}
        <ConfirmationModal
          isOpen={confirmation.isOpen}
          onClose={confirmation.hideConfirmation}
          onConfirm={confirmation.onConfirm}
          title={confirmation.title}
          message={confirmation.message}
          confirmText={confirmation.confirmText}
          cancelText={confirmation.cancelText}
          type={confirmation.type}
          isLoading={confirmation.isLoading}
        />
        </div>
      </div>
    </div>
  );
}